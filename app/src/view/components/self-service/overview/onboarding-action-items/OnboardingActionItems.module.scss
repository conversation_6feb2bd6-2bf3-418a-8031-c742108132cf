@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  padding: 24px;
  width: 100%;
}

.header {
  margin-bottom: 20px;
}

.title {
  color: $color-neutral-700;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 22px;
}

.subtitle {
  color: $color-neutral-600;
  font-size: 14px;
  margin: 0;
  line-height: 18px;
}

.actionItems {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.actionItem {
  border: 1px solid $color-neutral-300;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: $color-neutral-50;

  &:hover {
    border-color: $color-primary-500;
    background-color: $color-primary-50;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.actionItemContent {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.actionItemInfo {
  flex: 1;
  margin-right: 16px;
}

.actionItemTitle {
  color: $color-neutral-700;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  line-height: 20px;
}

.actionItemDescription {
  color: $color-neutral-600;
  font-size: 14px;
  margin: 0 0 8px 0;
  line-height: 18px;
}

.providerInfo {
  color: $color-neutral-500;
  font-size: 12px;
  margin: 0;
  line-height: 16px;
  font-style: italic;
}

.actionItemStatus {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.loader {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.error {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  color: $color-error-500;
  font-size: 14px;
}

// Responsive design
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .actionItemContent {
    flex-direction: column;
    align-items: flex-start;
  }

  .actionItemInfo {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .actionItemStatus {
    align-self: flex-end;
  }
}
